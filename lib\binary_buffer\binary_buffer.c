#include "binary_buffer.h"

#include <math.h>

static const unsigned char VT = 0x0B; // start of a message
static const unsigned char FS = 0x1C; // file separator
static const unsigned char CR = 0x0D; // linejump

BinaryBuffer *bb_create(unsigned char *buffer, size_t capacity) {
  BinaryBuffer *bb;
  LOG_CHECK_MALLOC(bb, malloc(sizeof(BinaryBuffer)));

  bb->buffer = buffer;
  bb->capacity = capacity;
  bb->length = 0;

  return bb;
}

BinaryBuffer *bb_from_string(const char *str, unsigned char *buffer,
                             size_t capacity) {
  size_t len = strlen(str);
  size_t needed_len = len + 3;
  if (needed_len > capacity) {
    last_error_set(
        "buffer too small to create message : needed %zu bytes, got %zu",
        needed_len, capacity);
    return NULL;
  }

  BinaryBuffer *bb = bb_create(buffer, capacity);
  if (!bb)
    return NULL;

  bb->buffer[0] = VT;
  memcpy(bb->buffer + 1, str, len);
  bb->buffer[len + 1] = FS;
  bb->buffer[len + 2] = CR;

  bb->length = needed_len;

  return bb;
}

int bb_append(BinaryBuffer *bb, const unsigned char *data, size_t length) {
  size_t new_length = bb->length + length;

  // Is there enough place in the buffer for storing new bytes ?
  if (new_length > bb->capacity) {
    last_error_set("not enough place in buffer to store data, dropping buffer");
    bb_clean(bb);
    return 1;
  }

  // adding bytes to the buffer
  memcpy(bb->buffer + bb->length, data, length);
  bb->length = new_length;

  return 0;
}

bool bb_is_message_complete(const BinaryBuffer *bb) {
  return (bb->buffer[bb->length - 3] == CR &&
          bb->buffer[bb->length - 2] == FS && bb->buffer[bb->length - 1] == CR);
}

BinaryBufferDocument **bb_to_documents_try(BinaryBuffer *bb) {
  size_t starts_count = 0;
  size_t ends_count = 0;
  bool last_was_start = false;

  // count files
  for (size_t i = 0; i < bb->length; i++)
    if (bb->buffer[i] == VT) {
      if (last_was_start) {
        last_error_set("two file starts without file end in between");
        return NULL;
      }
      last_was_start = true;
      starts_count++;
    } else if (bb->buffer[i] == FS) {
      if (!last_was_start) {
        last_error_set("two file ens without file start in between");
        return NULL;
      }
      last_was_start = false;
      ends_count++;
    }

  if (ends_count > starts_count) {
    last_error_set("more file ends than file starts (%zu > %zu)", ends_count,
                   starts_count);
    bb_clean(bb);
    return NULL;
  }

  // create strings
  BinaryBufferDocument **docs;
  LOG_CHECK_MALLOC(docs,
                   malloc((ends_count + 1) * sizeof(BinaryBufferDocument *)));

  size_t count = 0;

  for (size_t i = 0; i < bb->length && count < ends_count; i++)
    if (bb->buffer[i] == VT) {
      BinaryBufferDocument *doc;
      LOG_CHECK_MALLOC(doc, malloc(sizeof(BinaryBufferDocument)));
      doc->start = i;
      docs[count] = doc;
    } else if (bb->buffer[i] == FS) {
      docs[count]->end = i;
      count++;
    }

  docs[ends_count] = NULL;

  return docs;
}

const unsigned char *bb_document_to_bytes(BinaryBuffer *bb,
                                          const BinaryBufferDocument *doc,
                                          size_t *length) {
  bb->buffer[doc->start] = VT;
  bb->buffer[doc->end] = FS;
  *length = doc->end - doc->start + 1;

  return bb->buffer + doc->start;
}

const char *bb_document_to_string(BinaryBuffer *bb,
                                  const BinaryBufferDocument *doc) {
  bb->buffer[doc->end] = '\0';

  return (char *)(bb->buffer + (doc->start + 1));
}

void bb_clean(BinaryBuffer *bb) {
  bb->length = 0;
  memset(bb->buffer, 0, bb->capacity);
}

void bb_destruct(BinaryBuffer *bb) {
  if (!bb)
    return;

  free(bb);
}
